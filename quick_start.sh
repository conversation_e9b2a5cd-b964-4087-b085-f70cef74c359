#!/bin/bash
# BiteBase Intelligence Enhanced - Quick Start

echo "🚀 Starting BiteBase Intelligence Enhancement..."

# Start Backend
echo "🔧 Starting Backend..."
cd backend
python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

echo "Backend started with PID: $BACKEND_PID"

# Wait for backend to start
sleep 5

# Test backend health
echo "🔍 Testing backend health..."
curl -s http://localhost:8000/health || echo "Backend health check failed"

echo "✅ Backend is running on http://localhost:8000"
echo "📖 API docs available at http://localhost:8000/docs"

# Keep script running
echo "Press Ctrl+C to stop all services"
wait $BACKEND_PID
