#!/usr/bin/env python3
"""
Quick Fix and Test Script for BiteBase Intelligence Enhancement
"""

import subprocess
import time
import sys
import os
from pathlib import Path

def run_command(cmd, cwd=None, background=False):
    """Run a command and return the result"""
    try:
        if background:
            return subprocess.Popen(cmd, shell=True, cwd=cwd)
        else:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
            return result
    except Exception as e:
        print(f"Error running command: {e}")
        return None

def test_backend():
    """Test if backend can be imported and started"""
    print("🔍 Testing Backend...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # Test import
    result = run_command("python3 -c 'import app.main; print(\"Import successful\")'", cwd=backend_dir)
    if result and result.returncode == 0:
        print("✅ Backend imports successfully")
        return True
    else:
        print("❌ Backend import failed:")
        if result:
            print(result.stderr)
        return False

def test_frontend():
    """Test if frontend can be built"""
    print("🔍 Testing Frontend...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    
    # Check if node_modules exists
    if not (frontend_dir / "node_modules").exists():
        print("📦 Installing frontend dependencies...")
        result = run_command("npm install --legacy-peer-deps", cwd=frontend_dir)
        if result and result.returncode != 0:
            print("❌ npm install failed")
            return False
    
    # Try to run type check
    print("🔧 Running TypeScript check...")
    result = run_command("npm run check-types", cwd=frontend_dir)
    if result and result.returncode == 0:
        print("✅ TypeScript check passed")
        return True
    else:
        print("⚠️ TypeScript check failed:")
        if result:
            print(result.stderr)
        return False

def create_simple_start_script():
    """Create a simple start script"""
    script_content = '''#!/bin/bash
# BiteBase Intelligence Enhanced - Quick Start

echo "🚀 Starting BiteBase Intelligence Enhancement..."

# Start Backend
echo "🔧 Starting Backend..."
cd backend
python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

echo "Backend started with PID: $BACKEND_PID"

# Wait for backend to start
sleep 5

# Test backend health
echo "🔍 Testing backend health..."
curl -s http://localhost:8000/health || echo "Backend health check failed"

echo "✅ Backend is running on http://localhost:8000"
echo "📖 API docs available at http://localhost:8000/docs"

# Keep script running
echo "Press Ctrl+C to stop all services"
wait $BACKEND_PID
'''
    
    script_path = Path(__file__).parent / "quick_start.sh"
    with open(script_path, "w") as f:
        f.write(script_content)
    
    os.chmod(script_path, 0o755)
    print(f"✅ Created quick start script: {script_path}")

def main():
    print("🔧 BiteBase Intelligence Enhancement - Build & Test")
    print("=" * 50)
    
    # Test backend
    backend_ok = test_backend()
    
    # Test frontend
    frontend_ok = test_frontend()
    
    # Create start script
    create_simple_start_script()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print(f"Backend: {'✅ Ready' if backend_ok else '❌ Needs fixing'}")
    print(f"Frontend: {'✅ Ready' if frontend_ok else '⚠️ Has issues'}")
    
    if backend_ok:
        print("\n🚀 You can start the backend with:")
        print("   ./quick_start.sh")
        print("   OR")
        print("   cd backend && python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
    
    if not frontend_ok:
        print("\n⚠️ Frontend needs attention:")
        print("   - JSX syntax errors in InsightCard.tsx")
        print("   - Try running: cd frontend && npm run build")
    
    print("\n📝 Full status report available in: BUILD_STATUS_REPORT.md")

if __name__ == "__main__":
    main()
