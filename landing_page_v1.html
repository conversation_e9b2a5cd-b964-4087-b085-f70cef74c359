<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BiteBase Intelligence</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-20px)' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .emoji-float {
            animation: float 6s ease-in-out infinite;
            opacity: 0.7;
            pointer-events: none;
        }
        
        .emoji-float:nth-child(2n) {
            animation-delay: 0.5s;
        }
        
        .emoji-float:nth-child(3n) {
            animation-delay: 1s;
        }
        
        .gradient-text {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
    </style>
</head>
<body class="antialiased bg-gradient-to-br from-slate-50 via-white to-slate-100 min-h-screen overflow-x-hidden">
    <!-- Floating emojis background -->
    <div class="fixed inset-0 pointer-events-none overflow-hidden z-0">
        <div class="emoji-float absolute" style="left: 10%; top: 20%; font-size: 2rem;">🌮</div>
        <div class="emoji-float absolute" style="left: 85%; top: 15%; font-size: 1.8rem;">🍔</div>
        <div class="emoji-float absolute" style="left: 25%; top: 70%; font-size: 2.2rem;">🧆</div>
        <div class="emoji-float absolute" style="left: 75%; top: 5%; font-size: 1.5rem;">🍣</div>
        <div class="emoji-float absolute" style="left: 5%; top: 50%; font-size: 1.7rem;">🍕</div>
        <div class="emoji-float absolute" style="left: 90%; top: 60%; font-size: 2rem;">🍕</div>
        <div class="emoji-float absolute" style="left: 85%; top: -2%; font-size: 2.5rem;">🍝</div>
        <div class="emoji-float absolute" style="left: 70%; top: 10%; font-size: 1.6rem;">🍟</div>
        <div class="emoji-float absolute" style="left: 20%; top: 15%; font-size: 1.2rem;">🥘</div>
        <div class="emoji-float absolute" style="left: 8%; top: 35%; font-size: 1.7rem;">🥗</div>
    </div>

    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-xl border-b border-slate-200/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center gap-3 cursor-pointer">
                    <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white font-bold text-xl">🍽️</div>
                    <div>
                        <div class="font-bold text-xl bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">BiteBase</div>
                        <div class="text-xs text-slate-600">Intelligence</div>
                    </div>
                </div>
                
                <div class="hidden md:flex items-center gap-8">
                    <div class="flex items-center gap-6">
                        <a href="#features" class="relative text-slate-600 hover:text-orange-600 transition-colors duration-300 group font-medium">
                            Features
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="#analytics" class="relative text-slate-600 hover:text-orange-600 transition-colors duration-300 group font-medium">
                            Analytics
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="#pricing" class="relative text-slate-600 hover:text-orange-600 transition-colors duration-300 group font-medium">
                            Pricing
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="#contact" class="relative text-slate-600 hover:text-orange-600 transition-colors duration-300 group font-medium">
                            Contact
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                    <button class="inline-flex items-center justify-center gap-2 rounded-xl font-semibold transition-colors px-4 py-2 text-base text-white shadow-lg shadow-orange-500/25 hover:shadow-orange-500/40 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                        Get Started
                    </button>
                </div>
                
                <!-- Mobile menu button -->
                <button class="md:hidden text-slate-600 hover:text-orange-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-6 w-6">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div class="max-w-6xl mx-auto text-center relative z-10">
            <div class="inline-flex items-center gap-2 bg-orange-500/10 border border-orange-500/20 rounded-full px-6 py-3 mb-8 backdrop-blur-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles h-5 w-5 text-orange-500">
                    <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                    <path d="M20 3v4"></path>
                    <path d="M22 5h-4"></path>
                    <path d="M4 17v2"></path>
                    <path d="M5 18H3"></path>
                </svg>
                <span class="text-orange-600 font-semibold">AI-Powered Restaurant Intelligence</span>
            </div>
            
            <h1 class="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
                <span class="gradient-text bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900">BiteBase</span>
                <br>
                <span class="gradient-text bg-gradient-to-r from-orange-600 to-red-600">Intelligence</span>
            </h1>
            
            <p class="text-lg sm:text-xl md:text-2xl text-slate-600 mb-12 max-w-4xl mx-auto leading-relaxed">
                Transform your restaurant business with AI-powered analytics, location intelligence, and real-time market insights. Make data-driven decisions that drive growth.
            </p>
            
            <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
                <button class="inline-flex items-center justify-center gap-2 rounded-xl relative overflow-hidden hover:shadow-orange-500/40 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-6 sm:px-8 py-3 sm:py-4 text-lg font-semibold shadow-lg shadow-orange-500/25 transition-all duration-300">
                    <div class="flex items-center gap-2">
                        Start Free Trial
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right ml-2 h-5 w-5">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                        </svg>
                    </div>
                </button>
                
                <button class="border-2 border-slate-300 hover:border-slate-400 text-slate-700 hover:text-slate-900 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-lg transition-all bg-white/80 backdrop-blur-sm flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play h-5 w-5">
                        <polygon points="6 3 20 12 6 21 6 3"></polygon>
                    </svg>
                    Watch Demo
                </button>
            </div>
            
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-6 sm:gap-8 mb-16">
                <div class="text-center">
                    <div class="text-4xl mb-2">🏪</div>
                    <div class="text-3xl sm:text-4xl font-bold gradient-text bg-gradient-to-r from-orange-600 to-red-600 mb-2">1000+</div>
                    <div class="text-slate-600 font-medium">Restaurants Analyzed</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-2">📈</div>
                    <div class="text-3xl sm:text-4xl font-bold gradient-text bg-gradient-to-r from-orange-600 to-red-600 mb-2">25%</div>
                    <div class="text-slate-600 font-medium">Average Revenue Increase</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-2">⚡</div>
                    <div class="text-3xl sm:text-4xl font-bold gradient-text bg-gradient-to-r from-orange-600 to-red-600 mb-2">99.9%</div>
                    <div class="text-slate-600 font-medium">Platform Uptime</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-2">🌍</div>
                    <div class="text-3xl sm:text-4xl font-bold gradient-text bg-gradient-to-r from-orange-600 to-red-600 mb-2">50+</div>
                    <div class="text-slate-600 font-medium">Cities Covered</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="relative py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-white/50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 bg-green-500/10 border border-green-500/20 rounded-full px-6 py-3 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap h-5 w-5 text-green-600">
                        <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path>
                    </svg>
                    <span class="text-green-700 font-semibold">Powerful Features</span>
                </div>
                
                <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 gradient-text bg-gradient-to-r from-slate-900 to-slate-700">
                    Everything you need to succeed
                </h2>
                
                <p class="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto">
                    Comprehensive analytics, AI insights, and location intelligence all in one platform
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                    <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center text-white mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-6 sm:h-8 w-6 sm:w-8">
                            <path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                    </div>
                    
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-slate-800">Location Intelligence</h3>
                    
                    <p class="text-slate-600 leading-relaxed">
                        Advanced geospatial analytics for optimal restaurant placement and market analysis
                    </p>
                    
                    <div class="mt-6 flex items-center text-orange-600 font-semibold cursor-pointer">
                        Learn more 
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right ml-1 h-4 w-4">
                            <path d="m9 18 6-6-6-6"></path>
                        </svg>
                    </div>
                </div>
                
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                    <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain h-6 sm:h-8 w-6 sm:w-8">
                            <path d="M12 18V5"></path>
                            <path d="M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4"></path>
                            <path d="M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5"></path>
                            <path d="M17.997 5.125a4 4 0 0 1 2.526 5.77"></path>
                            <path d="M18 18a4 4 0 0 0 2-7.464"></path>
                            <path d="M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517"></path>
                            <path d="M6 18a4 4 0 0 1-2-7.464"></path>
                            <path d="M6.003 5.125a4 4 0 0 0-2.526 5.77"></path>
                        </svg>
                    </div>
                    
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-slate-800">AI-Powered Insights</h3>
                    
                    <p class="text-slate-600 leading-relaxed">
                        Smart recommendations and predictive analytics for revenue optimization
                    </p>
                    
                    <div class="mt-6 flex items-center text-orange-600 font-semibold cursor-pointer">
                        Learn more 
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right ml-1 h-4 w-4">
                            <path d="m9 18 6-6-6-6"></path>
                        </svg>
                    </div>
                </div>
                
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                    <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center text-white mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column h-6 sm:h-8 w-6 sm:w-8">
                            <path d="M3 3v16a2 2 0 0 0 2 2h16"></path>
                            <path d="M18 17V9"></path>
                            <path d="M13 17V5"></path>
                            <path d="M8 17v-3"></path>
                        </svg>
                    </div>
                    
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-slate-800">Real-time Analytics</h3>
                    
                    <p class="text-slate-600 leading-relaxed">
                        Live dashboards with performance metrics and competitive intelligence
                    </p>
                    
                    <div class="mt-6 flex items-center text-orange-600 font-semibold cursor-pointer">
                        Learn more 
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right ml-1 h-4 w-4">
                            <path d="m9 18 6-6-6-6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Analytics Section -->
    <section id="analytics" class="relative py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-slate-50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 bg-blue-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bar-chart-4 h-5 w-5 text-blue-600">
                        <path d="M3 3v18h18"></path>
                        <rect width="4" height="7" x="3" y="10" rx="1"></rect>
                        <rect width="4" height="12" x="10" y="5" rx="1"></rect>
                        <rect width="4" height="4" x="17" y="13" rx="1"></rect>
                    </svg>
                    <span class="text-blue-700 font-semibold">Advanced Analytics</span>
                </div>
                
                <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 gradient-text bg-gradient-to-r from-slate-900 to-slate-700">
                    Actionable insights at your fingertips
                </h2>
                
                <p class="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto">
                    Unlock the power of data with our comprehensive analytics platform
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 shadow-lg overflow-hidden">
                    <div class="relative h-64 sm:h-80 w-full bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-3/4 h-3/4 bg-white rounded-lg shadow-md flex items-center justify-center">
                                <div class="text-center p-4">
                                    <div class="text-blue-600 font-bold text-lg mb-2">Sample Dashboard</div>
                                    <div class="grid grid-cols-3 gap-2 mb-4">
                                        <div class="bg-blue-100 rounded p-2">
                                            <div class="text-blue-800 font-bold">$12.4K</div>
                                            <div class="text-xs text-blue-600">Daily Revenue</div>
                                        </div>
                                        <div class="bg-green-100 rounded p-2">
                                            <div class="text-green-800 font-bold">78%</div>
                                            <div class="text-xs text-green-600">Occupancy</div>
                                        </div>
                                        <div class="bg-orange-100 rounded p-2">
                                            <div class="text-orange-800 font-bold">4.8★</div>
                                            <div class="text-xs text-orange-600">Rating</div>
                                        </div>
                                    </div>
                                    <div class="h-32 bg-gradient-to-r from-blue-200 to-blue-300 rounded"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="space-y-6">
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up h-5 w-5">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                    <polyline points="16 7 22 7 22 13"></polyline>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 mb-2">Performance Tracking</h3>
                                <p class="text-slate-600">
                                    Monitor key metrics like revenue, customer satisfaction, and operational efficiency in real-time.
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-5 w-5">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 mb-2">Customer Insights</h3>
                                <p class="text-slate-600">
                                    Understand your customers' preferences, peak hours, and spending patterns to optimize operations.
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-check h-5 w-5">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path>
                                    <path d="m9 12 2 2 4-4"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 mb-2">Competitive Benchmarking</h3>
                                <p class="text-slate-600">
                                    Compare your performance against competitors in your area and industry standards.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="relative py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-orange-50 to-red-50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 bg-orange-500/10 border border-orange-500/20 rounded-full px-6 py-3 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag h-5 w-5 text-orange-500">
                        <path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2"></path>
                        <path d="M7 7h.01"></path>
                    </svg>
                    <span class="text-orange-600 font-semibold">Simple Pricing</span>
                </div>
                
                <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 gradient-text bg-gradient-to-r from-slate-900 to-slate-700">
                    Plans that grow with you
                </h2>
                
                <p class="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto">
                    Choose the perfect plan for your restaurant's needs
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 max-w-5xl mx-auto">
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 sm:p-8 shadow-lg">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-slate-800">Starter</h3>
                    <p class="text-slate-600 mb-6">Perfect for small restaurants getting started</p>
                    
                    <div class="text-3xl sm:text-4xl font-bold mb-6">
                        <span class="gradient-text bg-gradient-to-r from-slate-700 to-slate-900">$99</span>
                        <span class="text-lg text-slate-500">/month</span>
                    </div>
                    
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Basic analytics dashboard
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Up to 2 locations
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Weekly reports
                        </li>
                    </ul>
                    
                    <button class="w-full border-2 border-slate-300 hover:border-slate-400 text-slate-700 hover:text-slate-900 px-6 py-3 rounded-xl font-semibold transition-all bg-white/80 backdrop-blur-sm">
                        Get Started
                    </button>
                </div>
                
                <div class="bg-white/80 backdrop-blur-sm border-2 border-orange-500 rounded-2xl p-6 sm:p-8 shadow-lg relative overflow-hidden">
                    <div class="absolute top-4 right-4 bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                        Popular
                    </div>
                    
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-slate-800">Professional</h3>
                    <p class="text-slate-600 mb-6">Ideal for growing restaurant chains</p>
                    
                    <div class="text-3xl sm:text-4xl font-bold mb-6">
                        <span class="gradient-text bg-gradient-to-r from-orange-600 to-red-600">$299</span>
                        <span class="text-lg text-slate-500">/month</span>
                    </div>
                    
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Advanced analytics dashboard
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Up to 10 locations
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Daily reports
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            AI-powered recommendations
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Competitor benchmarking
                        </li>
                    </ul>
                    
                    <button class="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-6 py-3 rounded-xl font-semibold shadow-lg shadow-orange-500/25 hover:shadow-orange-500/40 transition-all">
                        Get Started
                    </button>
                </div>
                
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 sm:p-8 shadow-lg">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-slate-800">Enterprise</h3>
                    <p class="text-slate-600 mb-6">For large restaurant groups and franchises</p>
                    
                    <div class="text-3xl sm:text-4xl font-bold mb-6">
                        <span class="gradient-text bg-gradient-to-r from-slate-700 to-slate-900">Custom</span>
                    </div>
                    
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Unlimited locations
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Real-time analytics
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Custom AI models
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            Dedicated account manager
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            API access
                        </li>
                        <li class="flex items-center gap-2 text-slate-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-500">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            White-label solutions
                        </li>
                    </ul>
                    
                    <button class="w-full border-2 border-slate-300 hover:border-slate-400 text-slate-700 hover:text-slate-900 px-6 py-3 rounded-xl font-semibold transition-all bg-white/80 backdrop-blur-sm">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="relative py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-orange-500 to-red-500 text-white">
        <div class="max-w-4xl mx-auto text-center relative z-10">
            <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
                Ready to transform your restaurant?
            </h2>
            
            <p class="text-lg sm:text-xl mb-8 opacity-90">
                Join thousands of restaurants already using BiteBase Intelligence
            </p>
            
            <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                <button class="inline-flex items-center justify-center gap-2 rounded-xl relative overflow-hidden bg-white hover:bg-slate-100 text-orange-600 px-6 sm:px-8 py-3 sm:py-4 text-lg font-semibold shadow-lg transition-all duration-300">
                    <div class="flex items-center gap-2">
                        Start Free Trial
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right ml-2 h-5 w-5">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                        </svg>
                    </div>
                </button>
                
                <button class="border-2 border-white/30 hover:border-white/50 text-white hover:text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-lg transition-all bg-white/10 backdrop-blur-sm flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play h-5 w-5">
                        <polygon points="6 3 20 12 6 21 6 3"></polygon>
                    </svg>
                    Watch Demo
                </button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="relative py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 bg-blue-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-5 w-5 text-blue-600">
                        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                    </svg>
                    <span class="text-blue-700 font-semibold">Contact Us</span>
                </div>
                
                <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 gradient-text bg-gradient-to-r from-slate-900 to-slate-700">
                    Get in touch
                </h2>
                
                <p class="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto">
                    Have questions? Our team is here to help you get the most out of BiteBase Intelligence.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-5xl mx-auto">
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 sm:p-8 shadow-lg">
                    <h3 class="text-xl sm:text-2xl font-bold mb-6 text-slate-800">Send us a message</h3>
                    
                    <form class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-slate-700 mb-1">Name</label>
                            <input type="text" id="name" class="w-full px-4 py-3 rounded-xl border border-slate-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                            <input type="email" id="email" class="w-full px-4 py-3 rounded-xl border border-slate-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all">
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-slate-700 mb-1">Subject</label>
                            <select id="subject" class="w-full px-4 py-3 rounded-xl border border-slate-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all">
                                <option>General Inquiry</option>
                                <option>Sales</option>
                                <option>Support</option>
                                <option>Partnership</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-slate-700 mb-1">Message</label>
                            <textarea id="message" rows="4" class="w-full px-4 py-3 rounded-xl border border-slate-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all"></textarea>
                        </div>
                        
                        <button type="submit" class="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-6 py-3 rounded-xl font-semibold shadow-lg shadow-orange-500/25 hover:shadow-orange-500/40 transition-all">
                            Send Message
                        </button>
                    </form>
                </div>
                
                <div>
                    <div class="space-y-8">
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center text-orange-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-6 w-6">
                                    <path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path>
                                    <circle cx="12" cy="10" r="3"></circle>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 mb-2">Our Office</h3>
                                <p class="text-slate-600">
                                    123 Restaurant Lane<br>
                                    Foodie District, NY 10001<br>
                                    United States
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center text-blue-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-6 w-6">
                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 mb-2">Email Us</h3>
                                <p class="text-slate-600">
                                    <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700"><EMAIL></a><br>
                                    <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center text-green-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-6 w-6">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 mb-2">Call Us</h3>
                                <p class="text-slate-600">
                                    <a href="tel:+18005551234" class="text-orange-600 hover:text-orange-700">+****************</a><br>
                                    <span class="text-sm text-slate-500">Mon-Fri, 9am-6pm EST</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="relative py-12 sm:py-16 px-4 sm:px-6 lg:px-8 bg-slate-900 text-white">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 sm:gap-12">
                <div class="md:col-span-2">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white font-bold text-xl">🍽️</div>
                        <div>
                            <div class="font-bold text-xl">BiteBase</div>
                            <div class="text-slate-400">Intelligence</div>
                        </div>
                    </div>
                    
                    <p class="text-slate-400 mb-6">
                        Empowering restaurants with AI-driven insights and analytics to make smarter business decisions.
                    </p>
                    
                    <div class="flex items-center gap-4">
                        <a href="#" class="text-slate-400 hover:text-white transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter h-5 w-5">
                                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-slate-400 hover:text-white transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-facebook h-5 w-5">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-slate-400 hover:text-white transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin h-5 w-5">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect width="4" height="12" x="2" y="9"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </a>
                        <a href="#" class="text-slate-400 hover:text-white transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-instagram h-5 w-5">
                                <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                                <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-slate-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="#analytics" class="text-slate-400 hover:text-white transition-colors">Analytics</a></li>
                        <li><a href="#pricing" class="text-slate-400 hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">Integrations</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">API</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">Company</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">Press</a></li>
                        <li><a href="#contact" class="text-slate-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-slate-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-slate-500 mb-4 md:mb-0">
                    © 2024 BiteBase Intelligence. All rights reserved.
                </p>
                
                <div class="flex items-center gap-6">
                    <a href="#" class="text-slate-500 hover:text-slate-400 transition-colors text-sm">Privacy Policy</a>
                    <a href="#" class="text-slate-500 hover:text-slate-400 transition-colors text-sm">Terms of Service</a>
                    <a href="#" class="text-slate-500 hover:text-slate-400 transition-colors text-sm">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating chat button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button class="relative h-16 w-16 rounded-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div class="absolute inset-0 bg-white/20 scale-0 group-hover:scale-100 transition-transform duration-500 rounded-full"></div>
            <div class="relative z-10 flex items-center justify-center h-full w-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle h-6 w-6">
                    <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                </svg>
            </div>
        </button>
    </div>

    <script>
        // Simple animation for elements when they come into view
        document.addEventListener('DOMContentLoaded', function() {
            const animateOnScroll = function() {
                const elements = document.querySelectorAll('.animate-on-scroll');
                
                elements.forEach(element => {
                    const elementPosition = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;
                    
                    if (elementPosition < windowHeight - 100) {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }
                });
            };
            
            // Add animation class to elements that should animate
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                const headings = section.querySelectorAll('h2, h3, p, div');
                headings.forEach((heading, index) => {
                    heading.classList.add('animate-on-scroll');
                    heading.style.opacity = '0';
                    heading.style.transform = 'translateY(20px)';
                    heading.style.transition = `opacity 0.6s ease-out ${index * 0.1}s, transform 0.6s ease-out ${index * 0.1}s`;
                });
            });
            
            // Initial check
            animateOnScroll();
            
            // Check on scroll
            window.addEventListener('scroll', animateOnScroll);
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>